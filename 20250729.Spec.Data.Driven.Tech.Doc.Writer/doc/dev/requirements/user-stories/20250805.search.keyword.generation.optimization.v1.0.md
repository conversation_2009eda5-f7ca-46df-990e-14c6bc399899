# 搜索引擎搜索关键字生成优化需求分析

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-05
- **最后更新**: 2025-08-05
- **需求来源**: 用户反馈 - 搜索结果质量差，缺乏多语种搜索支持
- **优先级**: Must have (M)
- **相关文档**:
  - [多智能体技术调研报告编写系统 PRD](../20250731.multi.agent.tech.research.system.prd.v1.0.md)
  - [BaseAgent 基类设计文档](../../design/api/20250731.base.agent.design.v1.0.md)

## 1. 需求概述

### 1.1 问题描述
当前文档编写流程的需求分析阶段存在搜索引擎搜索关键字生成质量差的问题：

1. **关键字生成过于简单**：仅使用字符串分割，缺乏智能提取
2. **多语种支持不足**：没有中英文关键字互译和多语种搜索
3. **搜索质量评估机制不完善**：相关性评估硬编码，缺乏通用性
4. **关键字扩展能力有限**：缺乏同义词扩展和行业术语识别

### 1.2 业务影响
- **搜索结果质量差**：关键字不准确导致搜索结果相关性低
- **信息覆盖不全**：单语种搜索遗漏重要信息源
- **调研效率低**：需要人工干预优化搜索关键字
- **用户体验差**：系统自动化程度不足

### 1.3 优化目标
- **提升搜索质量**：智能关键字生成，提高搜索结果相关性
- **支持多语种搜索**：自动中英文关键字生成和搜索
- **增强扩展能力**：同义词扩展、行业术语识别
- **改善用户体验**：减少人工干预，提高自动化程度

## 2. 现状分析

### 2.1 当前实现分析

#### 2.1.1 关键字生成逻辑
```python
# 当前实现 - 过于简单
async def generate_search_keywords(analysis: RequirementAnalysis) -> SearchKeywords:
    # 从主题中提取关键词 - 仅使用split()
    topic_words = analysis.parsed_topic.split()
    primary_keywords.extend(topic_words[:3])
    
    # 从目标中提取关键词 - 简单长度过滤
    for objective in analysis.research_objectives:
        words = objective.split()
        technical_terms.extend([w for w in words if len(w) > 3])
```

**问题**：
- 没有智能分词和关键字提取
- 缺乏语义理解
- 没有去除停用词
- 缺乏关键字重要性评分

#### 2.1.2 多语种支持现状
```python
class SearchKeywords(BaseModel):
    english_keywords: List[str] = Field(default_factory=list, description="英文关键词")
    industry_terms: List[str] = Field(default_factory=list, description="行业术语")
```

**问题**：
- `english_keywords` 字段存在但未被填充
- 没有自动语言检测
- 缺乏中英文互译机制
- 没有多语种搜索策略

#### 2.1.3 搜索质量评估现状
```python
def _calculate_content_relevance(self, result: SearchResult) -> float:
    # 硬编码的网络安全关键词
    cybersecurity_keywords = [
        'cisa', 'cybersecurity', 'infrastructure', 'security', 'cyber',
        # ...
    ]
```

**问题**：
- 相关性评估硬编码，仅适用于特定主题
- 缺乏通用的主题相关性评估
- 没有动态关键字权重调整
- 缺乏搜索效果反馈机制

### 2.2 技术债务分析

1. **代码质量问题**：
   - 关键字生成逻辑过于简单
   - 硬编码的评估规则
   - 缺乏可扩展性

2. **功能缺失**：
   - 智能关键字提取
   - 多语种支持
   - 同义词扩展
   - 行业术语识别

3. **性能问题**：
   - 搜索结果相关性低
   - 需要多次搜索才能获得满意结果

## 3. 需求规格说明

### 3.1 功能需求

#### 3.1.1 智能关键字生成引擎 (P0)

**需求描述**：基于LLM的智能关键字提取和生成

**功能特性**：
- **智能分词**：支持中英文智能分词
- **关键字提取**：基于TF-IDF和语义分析的关键字提取
- **重要性评分**：为关键字分配重要性权重
- **去噪处理**：自动去除停用词和无意义词汇

**验收标准**：
- [ ] 支持中英文智能分词
- [ ] 关键字提取准确率 > 85%
- [ ] 自动去除停用词和标点符号
- [ ] 为关键字分配0.0-1.0的重要性评分
- [ ] 生成的关键字数量可配置（默认5-15个）

#### 3.1.2 多语种关键字生成 (P0)

**需求描述**：自动生成中英文关键字对，支持多语种搜索

**功能特性**：
- **语言检测**：自动检测输入文本的主要语言
- **关键字翻译**：中文关键字自动翻译为英文，反之亦然
- **双语搜索**：同时使用中英文关键字进行搜索
- **结果融合**：合并和去重多语种搜索结果

**验收标准**：
- [ ] 自动检测输入语言（中文/英文）
- [ ] 中文关键字自动翻译为英文
- [ ] 英文关键字自动翻译为中文
- [ ] 双语搜索结果正确融合和去重
- [ ] 翻译准确率 > 80%

#### 3.1.3 关键字扩展和优化 (P1)

**需求描述**：通过同义词扩展和行业术语识别增强关键字覆盖度

**功能特性**：
- **同义词扩展**：基于词典和语义模型的同义词扩展
- **行业术语识别**：识别和添加相关行业术语
- **搜索组合生成**：生成有效的关键字组合
- **动态优化**：基于搜索结果反馈优化关键字

**验收标准**：
- [ ] 为每个关键字生成2-5个同义词
- [ ] 自动识别行业相关术语
- [ ] 生成3-8个关键字组合
- [ ] 基于搜索结果质量动态调整关键字权重

#### 3.1.4 通用搜索质量评估 (P1)

**需求描述**：建立通用的搜索结果质量评估机制

**功能特性**：
- **主题相关性评估**：基于主题的动态相关性评估
- **内容质量评分**：评估搜索结果的内容质量
- **多维度评估**：综合考虑相关性、权威性、时效性
- **反馈机制**：收集用户反馈优化评估算法

**验收标准**：
- [ ] 支持任意主题的相关性评估
- [ ] 搜索结果质量评分准确率 > 80%
- [ ] 多维度评估指标（相关性、质量、时效性）
- [ ] 支持用户反馈和算法优化

### 3.2 非功能需求

#### 3.2.1 性能要求
- **响应时间**：关键字生成 < 10秒
- **搜索质量**：搜索结果相关性提升 > 30%
- **缓存命中率**：关键字翻译缓存命中率 > 70%
- **并发支持**：支持5+并发关键字生成任务

#### 3.2.2 可扩展性要求
- **语言支持**：架构支持扩展到其他语言
- **算法可插拔**：支持不同的关键字提取算法
- **配置驱动**：关键参数可通过配置调整

#### 3.2.3 可靠性要求
- **容错处理**：翻译服务失败时的降级机制
- **数据一致性**：关键字生成结果的一致性
- **错误恢复**：异常情况下的自动恢复

## 4. 技术方案

### 4.1 架构设计

#### 4.1.1 智能关键字生成器
```python
class IntelligentKeywordGenerator:
    """智能关键字生成器"""
    
    def __init__(self, llm_manager, translator, config):
        self.llm_manager = llm_manager
        self.translator = translator
        self.config = config
        self.keyword_extractor = KeywordExtractor()
        self.synonym_expander = SynonymExpander()
    
    async def generate_keywords(self, analysis: RequirementAnalysis) -> EnhancedSearchKeywords:
        """生成增强的搜索关键字"""
        # 1. 智能关键字提取
        extracted_keywords = await self._extract_intelligent_keywords(analysis)
        
        # 2. 多语种关键字生成
        multilingual_keywords = await self._generate_multilingual_keywords(extracted_keywords)
        
        # 3. 关键字扩展和优化
        expanded_keywords = await self._expand_and_optimize_keywords(multilingual_keywords)
        
        return expanded_keywords
```

#### 4.1.2 多语种翻译服务
```python
class MultilingualTranslationService:
    """多语种翻译服务"""
    
    async def translate_keywords(self, keywords: List[str], target_lang: str) -> List[TranslatedKeyword]:
        """翻译关键字"""
        # 使用LLM进行关键字翻译
        # 支持缓存和批量翻译
        pass
    
    def detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 使用语言检测库
        pass
```

### 4.2 数据模型扩展

```python
class EnhancedSearchKeywords(BaseModel):
    """增强的搜索关键字模型"""
    
    requirement_analysis_id: str
    
    # 基础关键字
    primary_keywords: List[WeightedKeyword] = Field(..., description="主要关键字（带权重）")
    secondary_keywords: List[WeightedKeyword] = Field(default_factory=list, description="次要关键字")
    technical_terms: List[WeightedKeyword] = Field(default_factory=list, description="技术术语")
    
    # 多语种关键字
    chinese_keywords: List[WeightedKeyword] = Field(default_factory=list, description="中文关键字")
    english_keywords: List[WeightedKeyword] = Field(default_factory=list, description="英文关键字")
    
    # 扩展关键字
    synonyms: List[SynonymGroup] = Field(default_factory=list, description="同义词组")
    industry_terms: List[WeightedKeyword] = Field(default_factory=list, description="行业术语")
    search_combinations: List[KeywordCombination] = Field(default_factory=list, description="搜索组合")
    
    # 元数据
    generation_metadata: KeywordGenerationMetadata = Field(..., description="生成元数据")

class WeightedKeyword(BaseModel):
    """带权重的关键字"""
    term: str
    weight: float = Field(ge=0.0, le=1.0, description="权重 (0.0-1.0)")
    language: str = Field(description="语言代码")
    category: str = Field(description="关键字类别")

class SynonymGroup(BaseModel):
    """同义词组"""
    primary_term: str
    synonyms: List[str]
    language: str

class KeywordCombination(BaseModel):
    """关键字组合"""
    keywords: List[str]
    combination_type: str  # "AND", "OR", "PHRASE"
    weight: float
```

### 4.3 实现计划

#### 4.3.1 Phase 1: 智能关键字生成 (2周)
- [ ] 实现基于LLM的关键字提取
- [ ] 添加中英文分词支持
- [ ] 实现关键字重要性评分
- [ ] 添加停用词过滤

#### 4.3.2 Phase 2: 多语种支持 (2周)
- [ ] 集成语言检测功能
- [ ] 实现关键字翻译服务
- [ ] 添加双语搜索支持
- [ ] 实现搜索结果融合

#### 4.3.3 Phase 3: 关键字扩展 (1.5周)
- [ ] 实现同义词扩展
- [ ] 添加行业术语识别
- [ ] 实现搜索组合生成
- [ ] 添加动态优化机制

#### 4.3.4 Phase 4: 质量评估优化 (1.5周)
- [ ] 实现通用相关性评估
- [ ] 添加多维度质量评分
- [ ] 实现反馈收集机制
- [ ] 优化评估算法

## 5. 验收标准

### 5.1 功能验收
- [ ] 智能关键字生成功能正常工作
- [ ] 多语种关键字自动生成和翻译
- [ ] 关键字扩展和同义词生成
- [ ] 通用搜索质量评估机制

### 5.2 性能验收
- [ ] 关键字生成时间 < 10秒
- [ ] 搜索结果相关性提升 > 30%
- [ ] 翻译缓存命中率 > 70%
- [ ] 支持5+并发任务

### 5.3 质量验收
- [ ] 关键字提取准确率 > 85%
- [ ] 翻译准确率 > 80%
- [ ] 搜索结果质量评分准确率 > 80%
- [ ] 代码覆盖率 > 80%

## 6. 风险评估

### 6.1 技术风险
- **LLM服务依赖**：关键字生成依赖LLM服务稳定性
- **翻译质量**：机器翻译可能存在准确性问题
- **性能影响**：智能处理可能增加响应时间

### 6.2 缓解措施
- **服务降级**：LLM服务失败时回退到简单算法
- **翻译缓存**：缓存常用翻译结果
- **异步处理**：使用异步处理提高性能

---

*本需求分析文档遵循项目 .augmentrules 规范，保存在 `doc/dev/requirements/user-stories/` 目录下。*
