# 需求分析阶段LLM调用优化需求分析

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-05
- **最后更新**: 2025-08-05
- **需求来源**: 用户反馈 - 需求分析阶段LLM调用次数过多，影响性能和成本
- **优先级**: Should have (S)
- **相关文档**:
  - [多智能体技术调研报告编写系统 PRD](../20250731.multi.agent.tech.research.system.prd.v1.0.md)
  - [BaseAgent 基类设计文档](../../design/api/20250731.base.agent.design.v1.0.md)
  - [LLM调用效率优化需求](../20250801.LLM.Efficiency.Optimization.v1.0.md)

## 1. 需求概述

### 1.1 问题描述
当前文档编写流程的需求分析阶段存在LLM调用次数过多的问题：

1. **调用次数过多**：单次需求分析需要5次独立的LLM调用
2. **信息重复传递**：每次调用都重复传递相同的用户请求信息
3. **上下文割裂**：各个分析步骤缺乏连贯性，可能产生不一致的结果
4. **性能影响**：多次调用导致总响应时间较长（5-15秒）
5. **成本较高**：多次LLM调用增加Token消耗和API成本

### 1.2 业务影响
- **响应时间长**：5次LLM调用导致用户等待时间过长
- **成本高昂**：重复调用增加Token消耗，提高运营成本
- **用户体验差**：长时间等待影响用户使用体验
- **资源浪费**：重复传递相同信息造成网络和计算资源浪费

### 1.3 优化目标
- **减少调用次数**：将5次调用优化为1-2次调用
- **提升响应速度**：总响应时间减少60-80%
- **降低成本**：Token消耗减少50-70%
- **保持质量**：确保分析结果的准确性和一致性

## 2. 现状分析

### 2.1 当前LLM调用链路分析

#### 2.1.1 调用流程
```python
async def _process(self, input_data: UserRequest, task_id: UUID) -> RequirementAnalysis:
    # 1. 解析用户意图 - LLM调用 #1
    parsed_intent = await self.intent_parser.parse(input_data)
    
    # 2. 生成调研目标 - LLM调用 #2
    research_objectives = await self.objective_generator.generate(input_data, parsed_intent)
    
    # 3. 定义调研范围 - 无LLM调用（规则基础）
    scope_definition = self.scope_generator.define(input_data, research_objectives)
    
    # 4. 分析目标受众 - LLM调用 #3
    target_audience = await self.audience_analyzer.analyze(input_data)
    
    # 5. 评估复杂度 - LLM调用 #4
    complexity = await self.complexity_assessor.assess(input_data, research_objectives)
    
    # 6. 推荐方法 - LLM调用 #5
    approach = await self.approach_recommender.recommend(input_data, complexity)
```

#### 2.1.2 各调用详细分析

**调用 #1: 用户意图解析**
- **输入Token**: ~200-400 (system prompt + user request)
- **输出Token**: ~300-600 (JSON格式的意图分析)
- **响应时间**: 2-4秒
- **目的**: 提取技术领域、深度要求、特殊关注点等

**调用 #2: 调研目标生成**
- **输入Token**: ~300-500 (system prompt + intent + user request)
- **输出Token**: ~200-400 (3-6个调研目标)
- **响应时间**: 2-3秒
- **目的**: 基于意图生成具体调研目标

**调用 #3: 目标受众分析**
- **输入Token**: ~200-300 (system prompt + user request)
- **输出Token**: ~50-100 (受众描述)
- **响应时间**: 1-2秒
- **目的**: 识别目标受众群体

**调用 #4: 复杂度评估**
- **输入Token**: ~250-400 (system prompt + user request + objectives)
- **输出Token**: ~20-50 (复杂度等级)
- **响应时间**: 1-2秒
- **目的**: 评估调研复杂度（低/中/高）

**调用 #5: 方法推荐**
- **输入Token**: ~200-350 (system prompt + user request + complexity)
- **输出Token**: ~50-150 (推荐方法)
- **响应时间**: 1-2秒
- **目的**: 推荐调研方法

#### 2.1.3 总体资源消耗
- **总调用次数**: 5次
- **总输入Token**: ~1,150-1,950
- **总输出Token**: ~620-1,300
- **总响应时间**: 7-13秒
- **网络往返**: 5次

### 2.2 问题根因分析

1. **架构设计问题**：
   - 各个分析器独立设计，缺乏整体考虑
   - 没有考虑LLM调用的批量优化

2. **信息重复问题**：
   - 用户请求信息在每次调用中重复传递
   - 缺乏上下文共享机制

3. **依赖关系问题**：
   - 部分分析任务存在依赖关系，无法并行
   - 串行执行增加总响应时间

4. **粒度设计问题**：
   - 分析任务粒度过细，适合合并处理
   - 缺乏批量处理的设计考虑

## 3. 需求规格说明

### 3.1 功能需求

#### 3.1.1 统一需求分析引擎 (P0)

**需求描述**：将多个独立的分析任务合并为统一的LLM调用

**功能特性**：
- **一次性分析**：单次LLM调用完成所有分析任务
- **结构化输出**：返回包含所有分析结果的结构化数据
- **上下文一致性**：确保各分析结果之间的逻辑一致性
- **错误处理**：支持部分失败时的降级处理

**验收标准**：
- [ ] 将5次LLM调用合并为1次调用
- [ ] 输出包含所有必需的分析结果
- [ ] 分析结果格式与现有接口兼容
- [ ] 支持JSON格式的结构化输出
- [ ] 错误处理机制完善

#### 3.1.2 智能批量处理 (P1)

**需求描述**：对于无法合并的任务，实现智能批量处理

**功能特性**：
- **并行调用**：独立任务并行执行
- **依赖管理**：自动处理任务间依赖关系
- **结果聚合**：统一收集和处理结果
- **超时控制**：避免单个任务影响整体性能

**验收标准**：
- [ ] 支持最多3个并行LLM调用
- [ ] 自动识别任务依赖关系
- [ ] 总响应时间不超过最慢任务的1.2倍
- [ ] 支持部分任务失败的容错处理

#### 3.1.3 上下文共享机制 (P1)

**需求描述**：建立分析任务间的上下文共享机制

**功能特性**：
- **信息复用**：避免重复传递相同信息
- **上下文传递**：前置任务结果作为后续任务输入
- **状态管理**：维护分析过程中的状态信息
- **缓存优化**：缓存中间结果避免重复计算

**验收标准**：
- [ ] 用户请求信息仅传递一次
- [ ] 前置分析结果可被后续任务使用
- [ ] 支持分析状态的持久化
- [ ] 中间结果缓存命中率 > 30%

#### 3.1.4 性能监控和优化 (P2)

**需求描述**：建立LLM调用性能监控和自动优化机制

**功能特性**：
- **调用监控**：记录每次LLM调用的性能指标
- **性能分析**：分析调用模式和性能瓶颈
- **自动优化**：基于历史数据优化调用策略
- **报告生成**：生成性能优化报告

**验收标准**：
- [ ] 记录所有LLM调用的详细指标
- [ ] 提供性能分析和优化建议
- [ ] 支持调用策略的动态调整
- [ ] 生成定期性能报告

### 3.2 非功能需求

#### 3.2.1 性能要求
- **响应时间**：总响应时间减少60-80%，目标 < 5秒
- **Token消耗**：总Token消耗减少50-70%
- **并发支持**：支持5+并发需求分析任务
- **缓存命中率**：分析结果缓存命中率 > 40%

#### 3.2.2 可靠性要求
- **容错处理**：单个分析任务失败不影响整体流程
- **降级机制**：LLM服务异常时的备用处理方案
- **数据一致性**：确保分析结果的逻辑一致性
- **错误恢复**：支持分析过程的断点续传

#### 3.2.3 兼容性要求
- **接口兼容**：与现有RequirementAnalysis模型完全兼容
- **向后兼容**：支持现有代码的平滑迁移
- **配置兼容**：支持现有配置参数和格式

## 4. 技术方案

### 4.1 架构设计

#### 4.1.1 统一分析引擎
```python
class UnifiedRequirementAnalyzer:
    """统一需求分析引擎"""
    
    async def analyze_comprehensive(self, request: UserRequest) -> RequirementAnalysis:
        """一次性完成所有需求分析任务"""
        
        # 构建统一的分析prompt
        unified_prompt = self._build_unified_prompt(request)
        
        # 单次LLM调用获取所有分析结果
        response = await self.llm_caller(unified_prompt, 
                                       temperature=0.1, 
                                       max_tokens=2000)
        
        # 解析结构化结果
        analysis_results = self._parse_unified_response(response)
        
        # 构建RequirementAnalysis对象
        return self._build_requirement_analysis(request, analysis_results)
    
    def _build_unified_prompt(self, request: UserRequest) -> List[Dict[str, str]]:
        """构建统一分析prompt"""
        return [
            {
                "role": "system",
                "content": """你是一个专业的需求分析专家。请对用户的调研需求进行全面分析，
                包括：用户意图解析、调研目标生成、目标受众分析、复杂度评估、方法推荐。
                
                请以JSON格式返回分析结果，包含以下字段：
                {
                    "intent_analysis": {
                        "topic": "解析后的主题",
                        "technical_domains": ["技术领域1", "技术领域2"],
                        "depth_requirement": "深度要求",
                        "special_focus": ["特殊关注点1", "特殊关注点2"],
                        "research_type": "调研类型"
                    },
                    "research_objectives": ["目标1", "目标2", "目标3"],
                    "target_audience": "目标受众描述",
                    "complexity_level": "复杂度等级(低/中/高)",
                    "recommended_approach": "推荐方法"
                }"""
            },
            {
                "role": "user", 
                "content": f"""请分析以下调研需求：
                
                主题：{request.topic}
                调研范围：{request.scope}
                输出格式：{request.format}
                
                请提供完整的需求分析结果。"""
            }
        ]
```

#### 4.1.2 智能批量处理器
```python
class BatchAnalysisProcessor:
    """批量分析处理器"""
    
    async def process_batch(self, request: UserRequest) -> RequirementAnalysis:
        """批量处理分析任务"""
        
        # 第一批：基础分析（并行）
        basic_tasks = [
            self._analyze_intent(request),
            self._analyze_audience(request)
        ]
        intent_result, audience_result = await asyncio.gather(*basic_tasks)
        
        # 第二批：依赖分析（基于第一批结果）
        dependent_tasks = [
            self._generate_objectives(request, intent_result),
            self._assess_complexity(request, intent_result),
        ]
        objectives_result, complexity_result = await asyncio.gather(*dependent_tasks)
        
        # 第三批：最终分析
        approach_result = await self._recommend_approach(request, complexity_result)
        
        # 合并结果
        return self._merge_results(request, {
            'intent': intent_result,
            'objectives': objectives_result,
            'audience': audience_result,
            'complexity': complexity_result,
            'approach': approach_result
        })
```

### 4.2 数据模型优化

```python
class UnifiedAnalysisRequest(BaseModel):
    """统一分析请求模型"""
    user_request: UserRequest
    analysis_scope: List[str] = Field(default_factory=lambda: [
        "intent_analysis", "objectives_generation", "audience_analysis",
        "complexity_assessment", "approach_recommendation"
    ])
    optimization_level: str = Field(default="unified", description="优化级别：unified/batch/parallel")

class UnifiedAnalysisResponse(BaseModel):
    """统一分析响应模型"""
    intent_analysis: Dict[str, Any]
    research_objectives: List[str]
    target_audience: str
    complexity_level: str
    recommended_approach: str
    analysis_metadata: AnalysisMetadata

class AnalysisMetadata(BaseModel):
    """分析元数据"""
    llm_calls_count: int
    total_tokens_used: int
    response_time_seconds: float
    optimization_method: str
    cache_hits: int
```

### 4.3 实现计划

#### 4.3.1 Phase 1: 统一分析引擎 (1.5周)
- [ ] 设计统一的prompt模板
- [ ] 实现结构化响应解析
- [ ] 添加错误处理和降级机制
- [ ] 完成单元测试

#### 4.3.2 Phase 2: 批量处理优化 (1周)
- [ ] 实现任务依赖分析
- [ ] 添加并行处理支持
- [ ] 实现结果聚合机制
- [ ] 性能测试和调优

#### 4.3.3 Phase 3: 上下文共享 (0.5周)
- [ ] 实现上下文传递机制
- [ ] 添加中间结果缓存
- [ ] 优化信息复用逻辑
- [ ] 集成测试

#### 4.3.4 Phase 4: 监控和优化 (0.5周)
- [ ] 添加性能监控
- [ ] 实现自动优化机制
- [ ] 生成优化报告
- [ ] 文档更新

## 5. 预期效果

### 5.1 性能提升
- **调用次数**：从5次减少到1-2次（减少60-80%）
- **响应时间**：从7-13秒减少到2-5秒（减少60-80%）
- **Token消耗**：减少50-70%
- **网络往返**：从5次减少到1-2次

### 5.2 成本优化
- **API调用成本**：减少60-80%
- **网络带宽**：减少重复数据传输
- **计算资源**：提高资源利用效率

### 5.3 用户体验
- **等待时间**：显著减少用户等待时间
- **响应一致性**：提高分析结果的逻辑一致性
- **系统稳定性**：减少网络异常对系统的影响

## 6. 验收标准

### 6.1 功能验收
- [ ] 统一分析引擎正常工作
- [ ] 分析结果与现有格式完全兼容
- [ ] 错误处理和降级机制有效
- [ ] 批量处理功能正常

### 6.2 性能验收
- [ ] LLM调用次数减少 > 60%
- [ ] 总响应时间减少 > 60%
- [ ] Token消耗减少 > 50%
- [ ] 缓存命中率 > 40%

### 6.3 质量验收
- [ ] 分析结果准确率 > 95%
- [ ] 结果一致性评分 > 90%
- [ ] 错误处理覆盖率 > 95%
- [ ] 代码覆盖率 > 80%

## 7. 风险评估

### 7.1 技术风险
- **复杂prompt风险**：统一prompt可能过于复杂，影响LLM理解
- **结果解析风险**：结构化输出解析可能失败
- **兼容性风险**：优化后可能影响现有功能

### 7.2 缓解措施
- **渐进式优化**：先实现部分合并，逐步优化
- **备用机制**：保留原有分析器作为备用方案
- **充分测试**：全面测试确保兼容性

---

*本需求分析文档遵循项目 .augmentrules 规范，保存在 `doc/dev/requirements/user-stories/` 目录下。*
